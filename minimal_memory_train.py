import numpy as np
from n2v.models import N2VConfig, N2V
from n2v.internals.N2V_DataGenerator import N2V_DataGenerator
from astropy.io import fits
import glob
import os
import tensorflow as tf
import gc
import psutil

def print_memory_usage(stage=""):
    """打印当前内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    memory_mb = memory_info.rss / 1024 / 1024
    print(f"[{stage}] 内存使用: {memory_mb:.1f} MB")

# GPU配置
print("=== GPU配置 ===")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"配置了{len(gpus)}个GPU")
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")

print_memory_usage("初始化")

def minimal_train():
    """极简内存使用的训练方案"""
    
    # 只使用3个文件进行训练
    data_path = "data"
    fits_files = glob.glob(os.path.join(data_path, "*.fit*"))[:3]
    
    print(f"使用 {len(fits_files)} 个文件进行训练")
    
    # 生成极少量的patches
    all_patches = []
    target_patches = 200  # 总共只生成200个patches
    patches_per_file = target_patches // len(fits_files)
    
    for i, file_path in enumerate(fits_files):
        print(f"\n处理文件 {i+1}: {os.path.basename(file_path)}")
        
        # 加载图像
        with fits.open(file_path) as hdul:
            image = hdul[0].data.astype(np.float32)
            print(f"图像形状: {image.shape}")
        
        # 添加通道维度
        image = image[..., np.newaxis]
        
        # 生成patches
        datagen = N2V_DataGenerator()
        patches = datagen.generate_patches(image, shape=(64, 64), augment=False)
        
        # 随机选择少量patches
        if len(patches) > patches_per_file:
            indices = np.random.choice(len(patches), patches_per_file, replace=False)
            patches = patches[indices]
        
        all_patches.extend(patches)
        print(f"生成了 {len(patches)} 个patches")
        
        # 立即清理
        del image, patches
        gc.collect()
        print_memory_usage(f"文件{i+1}处理完成")
    
    # 转换为数组
    patches_array = np.array(all_patches)
    del all_patches
    gc.collect()
    
    print(f"\n总patches数量: {len(patches_array)}")
    print(f"Patches形状: {patches_array.shape}")
    print_memory_usage("patches生成完成")
    
    # 分割数据
    split_idx = int(0.8 * len(patches_array))  # 80/20分割
    X_train = patches_array[:split_idx]
    X_val = patches_array[split_idx:]
    
    del patches_array
    gc.collect()
    
    print(f"训练数据: {X_train.shape}")
    print(f"验证数据: {X_val.shape}")
    print_memory_usage("数据分割完成")
    
    # 极简模型配置
    print("\n=== 配置模型 ===")
    config = N2VConfig(X_train,
                       unet_kern_size=3,
                       train_steps_per_epoch=20,   # 极少的步数
                       train_epochs=20,            # 极少的轮数
                       train_loss='mse',
                       batch_size=2,               # 极小的批量大小
                       train_batch_size=2,
                       n2v_perc_pix=0.198,
                       n2v_patch_shape=(64, 64),
                       n2v_manipulator='uniform_withCP',
                       n2v_neighborhood_radius=5,
                       train_checkpoint='weights_minimal.weights.h5',
                       train_learning_rate=0.001)
    
    print("模型配置:")
    print(f"- 批量大小: {config.batch_size}")
    print(f"- 每轮步数: {config.train_steps_per_epoch}")
    print(f"- 训练轮数: {config.train_epochs}")
    
    # 创建模型
    print("\n=== 创建模型 ===")
    model = N2V(config, 'n2v_minimal', basedir='models')
    print_memory_usage("模型创建完成")
    
    # 训练
    print("\n=== 开始训练 ===")
    try:
        history = model.train(X_train, X_val)
        print("训练完成!")
        
        # 保存历史
        os.makedirs('models', exist_ok=True)
        np.save('models/minimal_training_history.npy', history.history)
        print("训练历史已保存")
        
    except Exception as e:
        print(f"训练错误: {e}")
        print("\n如果仍然出现内存错误，请尝试:")
        print("1. 减少文件数量到2个或1个")
        print("2. 减少target_patches到100或50")
        print("3. 将batch_size设置为1")
        
    finally:
        try:
            del X_train, X_val
        except:
            pass
        gc.collect()
        print_memory_usage("最终清理完成")

if __name__ == "__main__":
    print("=== 极简内存Noise2Void训练 ===")
    minimal_train()
