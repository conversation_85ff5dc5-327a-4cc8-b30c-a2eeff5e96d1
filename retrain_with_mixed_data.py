import numpy as np
from n2v.models import N2VConfig, N2V
from astropy.io import fits
import glob
import os
import tensorflow as tf
import gc
import psutil

def print_memory_usage(stage=""):
    """打印当前内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    memory_mb = memory_info.rss / 1024 / 1024
    print(f"[{stage}] 内存使用: {memory_mb:.1f} MB")

# GPU配置
print("=== GPU配置 ===")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"配置了{len(gpus)}个GPU")
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")

def normalize_image_intensity(image, target_mean=None, target_std=None):
    """
    标准化图像强度，解决域偏移问题
    """
    current_mean = np.mean(image)
    current_std = np.std(image)
    
    if target_mean is None:
        target_mean = current_mean
    if target_std is None:
        target_std = current_std
    
    # Z-score标准化然后重新缩放
    normalized = (image - current_mean) / current_std
    rescaled = normalized * target_std + target_mean
    
    return rescaled.astype(image.dtype)

def extract_patches_from_mixed_data(data_dirs, patch_size=64, stride=32, patches_per_image=40):
    """
    从混合数据源提取patches，包含强度标准化
    """
    print("=== 混合数据Patch提取 ===")
    
    all_patches = []
    all_files = []
    
    # 收集所有文件
    for data_dir in data_dirs:
        if os.path.exists(data_dir):
            files = glob.glob(os.path.join(data_dir, "*.fit*"))
            all_files.extend([(f, data_dir) for f in files])
    
    if len(all_files) == 0:
        raise ValueError("没有找到任何FITS文件!")
    
    print(f"找到总共 {len(all_files)} 个文件")
    
    # 计算目标强度统计（使用训练数据作为参考）
    print("计算目标强度统计...")
    train_files = [f for f, d in all_files if 'data' in d]
    if len(train_files) > 0:
        with fits.open(train_files[0]) as hdul:
            sample_data = hdul[0].data.astype(np.float32)
        target_mean = np.mean(sample_data)
        target_std = np.std(sample_data)
        print(f"目标强度 - 均值: {target_mean:.1f}, 标准差: {target_std:.1f}")
    else:
        target_mean = None
        target_std = None
    
    # 处理每个文件
    for i, (file_path, data_dir) in enumerate(all_files):
        print(f"\n处理文件 {i+1}/{len(all_files)}: {os.path.basename(file_path)}")
        print(f"来源: {data_dir}")
        
        try:
            # 加载图像
            with fits.open(file_path) as hdul:
                image = hdul[0].data.astype(np.float32)
            
            print(f"原始图像统计 - 均值: {np.mean(image):.1f}, 标准差: {np.std(image):.1f}")
            
            # 强度标准化（仅对real_data）
            if 'real_data' in data_dir and target_mean is not None:
                image = normalize_image_intensity(image, target_mean, target_std)
                print(f"标准化后统计 - 均值: {np.mean(image):.1f}, 标准差: {np.std(image):.1f}")
            
            # 提取patches
            h, w = image.shape
            y_positions = list(range(0, h - patch_size + 1, stride))
            x_positions = list(range(0, w - patch_size + 1, stride))
            
            all_positions = [(y, x) for y in y_positions for x in x_positions]
            
            if len(all_positions) > patches_per_image:
                selected_indices = np.random.choice(len(all_positions), patches_per_image, replace=False)
                selected_positions = [all_positions[i] for i in selected_indices]
            else:
                selected_positions = all_positions
            
            image_patches = []
            for y, x in selected_positions:
                patch = image[y:y+patch_size, x:x+patch_size]
                patch_with_channel = patch[..., np.newaxis]
                image_patches.append(patch_with_channel)
            
            all_patches.extend(image_patches)
            print(f"提取了 {len(image_patches)} 个patches")
            
            # 清理内存
            del image, image_patches
            gc.collect()
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue
    
    print(f"\n总共提取了 {len(all_patches)} 个patches")
    return np.array(all_patches)

def train_mixed_model():
    """使用混合数据训练改进的模型"""
    
    print("=== 混合数据训练 ===")
    print_memory_usage("开始")
    
    # 数据目录
    data_dirs = ["data", "real_data"]
    
    # 提取patches
    patches = extract_patches_from_mixed_data(data_dirs, 
                                            patch_size=64, 
                                            stride=32, 
                                            patches_per_image=50)
    
    print(f"Patches形状: {patches.shape}")
    print_memory_usage("patches提取完成")
    
    # 分割数据
    split_idx = int(0.8 * len(patches))
    X_train = patches[:split_idx]
    X_val = patches[split_idx:]
    
    print(f"训练数据: {X_train.shape}")
    print(f"验证数据: {X_val.shape}")
    
    del patches
    gc.collect()
    print_memory_usage("数据分割完成")
    
    # 配置模型
    print("\n=== 配置改进模型 ===")
    config = N2VConfig(X_train,
                       unet_kern_size=3,
                       train_steps_per_epoch=30,
                       train_epochs=40,  # 增加训练轮数
                       train_loss='mse',
                       batch_size=4,
                       train_batch_size=4,
                       n2v_perc_pix=0.198,
                       n2v_patch_shape=(64, 64),
                       n2v_manipulator='uniform_withCP',
                       n2v_neighborhood_radius=5,
                       train_checkpoint='weights_mixed_best.weights.h5',
                       train_learning_rate=0.0006)  # 稍微降低学习率
    
    print("模型配置:")
    print(f"- 训练数据数量: {len(X_train)}")
    print(f"- 验证数据数量: {len(X_val)}")
    print(f"- 批量大小: {config.batch_size}")
    print(f"- 训练轮数: {config.train_epochs}")
    
    # 创建模型
    print("\n=== 创建模型 ===")
    model = N2V(config, 'n2v_mixed_data', basedir='models')
    print_memory_usage("模型创建完成")
    
    # 训练
    print("\n=== 开始训练 ===")
    try:
        history = model.train(X_train, X_val)
        print("训练完成!")
        
        # 保存历史
        os.makedirs('models', exist_ok=True)
        np.save('models/mixed_training_history.npy', history.history)
        print("训练历史已保存")
        
    except Exception as e:
        print(f"训练错误: {e}")
        
    finally:
        try:
            del X_train, X_val
        except:
            pass
        gc.collect()
        print_memory_usage("训练完成")

def test_mixed_model():
    """测试混合数据训练的模型"""
    
    print("\n=== 测试混合数据模型 ===")
    
    try:
        model = N2V(config=None, name='n2v_mixed_data', basedir='models')
        print("✅ 混合数据模型加载成功!")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 测试真实数据
    real_files = glob.glob(os.path.join("real_data", "*.fit*"))
    if len(real_files) == 0:
        print("❌ 没有找到真实数据文件")
        return
    
    test_file = real_files[0]
    print(f"测试文件: {os.path.basename(test_file)}")
    
    with fits.open(test_file) as hdul:
        test_image = hdul[0].data.astype(np.float32)
    
    # 裁剪测试区域
    h, w = test_image.shape
    crop_size = 512
    start_h = (h - crop_size) // 2
    start_w = (w - crop_size) // 2
    test_crop = test_image[start_h:start_h+crop_size, start_w:start_w+crop_size]
    
    print(f"测试区域统计:")
    print(f"  范围: {test_crop.min():.1f} - {test_crop.max():.1f}")
    print(f"  均值: {test_crop.mean():.1f}, 标准差: {test_crop.std():.1f}")
    
    # 预测
    test_input = test_crop[..., np.newaxis]
    denoised = model.predict(test_input, axes='YXC')
    denoised_2d = denoised[..., 0]
    
    # 计算统计
    psnr = 20 * np.log10(np.max(test_crop) / np.sqrt(np.mean((test_crop - denoised_2d) ** 2)))
    noise_reduction = np.std(test_crop) - np.std(denoised_2d)
    
    print(f"混合模型结果:")
    print(f"  去噪范围: {denoised_2d.min():.1f} - {denoised_2d.max():.1f}")
    print(f"  PSNR: {psnr:.2f} dB")
    print(f"  噪声减少: {noise_reduction:.2f}")
    
    # 保存结果
    os.makedirs('mixed_model_results', exist_ok=True)
    
    original_fits = fits.PrimaryHDU(test_crop)
    original_fits.writeto('mixed_model_results/original.fits', overwrite=True)
    
    denoised_fits = fits.PrimaryHDU(denoised_2d)
    denoised_fits.writeto('mixed_model_results/denoised_mixed.fits', overwrite=True)
    
    print("✅ 结果已保存到 mixed_model_results/ 目录")

if __name__ == "__main__":
    train_mixed_model()
    test_mixed_model()
