import os
import sys

def show_menu():
    """显示主菜单"""
    print("=" * 60)
    print("🌟 Noise2Void 天文图像去噪系统")
    print("=" * 60)
    print()
    print("📋 可用功能:")
    print()
    print("1. 🔧 系统检查")
    print("   - 检查Python环境和依赖包")
    print("   - 检查数据文件")
    print("   - 内存和GPU状态")
    print()
    print("2. 🎯 模型训练")
    print("   - 手动patch训练 (推荐)")
    print("   - 内存优化训练")
    print("   - 极简内存训练")
    print()
    print("3. 🧪 模型测试")
    print("   - 测试训练好的模型")
    print("   - 生成对比图和统计信息")
    print()
    print("4. 🚀 批量处理")
    print("   - 对所有FITS文件进行去噪")
    print("   - 自动分块处理大图像")
    print()
    print("5. 📊 查看训练历史")
    print("   - 显示训练过程和损失曲线")
    print()
    print("6. 📖 帮助文档")
    print("   - 查看详细使用说明")
    print()
    print("0. 退出")
    print()

def run_command(command):
    """运行系统命令"""
    python_path = "C:\\Python\\Python310\\python.exe"
    full_command = f"{python_path} {command}"
    print(f"执行命令: {full_command}")
    print("-" * 50)
    os.system(full_command)
    print("-" * 50)
    input("按回车键继续...")

def show_training_history():
    """显示训练历史"""
    import numpy as np
    import matplotlib.pyplot as plt
    
    history_files = [
        "models/manual_training_history.npy",
        "models/training_history.npy",
        "models/training_history_optimized.npy"
    ]
    
    found_history = False
    for history_file in history_files:
        if os.path.exists(history_file):
            try:
                history = np.load(history_file, allow_pickle=True).item()
                
                plt.figure(figsize=(12, 4))
                
                plt.subplot(1, 2, 1)
                plt.plot(history['loss'], label='训练损失')
                plt.plot(history['val_loss'], label='验证损失')
                plt.title('训练损失曲线')
                plt.xlabel('轮数')
                plt.ylabel('损失')
                plt.legend()
                plt.grid(True)
                
                plt.subplot(1, 2, 2)
                if 'n2v_abs' in history:
                    plt.plot(history['n2v_abs'], label='训练MAE')
                    plt.plot(history['val_n2v_abs'], label='验证MAE')
                    plt.title('平均绝对误差')
                    plt.xlabel('轮数')
                    plt.ylabel('MAE')
                    plt.legend()
                    plt.grid(True)
                
                plt.tight_layout()
                plt.savefig(f'training_history_{os.path.basename(history_file)}.png', 
                           dpi=150, bbox_inches='tight')
                plt.show()
                
                print(f"✅ 训练历史图已保存: training_history_{os.path.basename(history_file)}.png")
                found_history = True
                break
                
            except Exception as e:
                print(f"读取 {history_file} 时出错: {e}")
    
    if not found_history:
        print("❌ 没有找到训练历史文件")
    
    input("按回车键继续...")

def show_help():
    """显示帮助信息"""
    print("=" * 60)
    print("📖 Noise2Void 使用帮助")
    print("=" * 60)
    print()
    print("🔧 系统要求:")
    print("- Python 3.10+")
    print("- TensorFlow 2.15+")
    print("- Noise2Void")
    print("- Astropy")
    print("- NumPy, Matplotlib")
    print()
    print("📁 目录结构:")
    print("- data/          : 输入的FITS文件")
    print("- models/        : 训练好的模型")
    print("- test_results/  : 测试结果")
    print("- denoised_results/ : 批量处理结果")
    print()
    print("🎯 训练建议:")
    print("- 首次使用推荐选择'手动patch训练'")
    print("- 如果内存不足，选择'极简内存训练'")
    print("- 训练时间约15-30分钟")
    print()
    print("🚀 处理建议:")
    print("- 先用'模型测试'验证效果")
    print("- 再用'批量处理'处理所有图像")
    print("- 大图像会自动分块处理")
    print()
    print("❓ 常见问题:")
    print("- 内存不足: 减少批量大小或使用极简训练")
    print("- GPU错误: 检查CUDA和驱动安装")
    print("- 模型加载失败: 确保训练完成并保存成功")
    print()
    input("按回车键继续...")

def main():
    """主程序"""
    while True:
        os.system('cls' if os.name == 'nt' else 'clear')
        show_menu()
        
        try:
            choice = input("请选择功能 (0-6): ").strip()
            
            if choice == '0':
                print("👋 感谢使用！")
                break
            elif choice == '1':
                run_command("check_system_resources.py")
            elif choice == '2':
                run_command("run_training.bat")
            elif choice == '3':
                run_command("test_trained_model.py")
            elif choice == '4':
                run_command("batch_denoise.py")
            elif choice == '5':
                show_training_history()
            elif choice == '6':
                show_help()
            else:
                print("❌ 无效选择，请输入0-6之间的数字")
                input("按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n👋 感谢使用！")
            break
        except Exception as e:
            print(f"❌ 出现错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
