import numpy as np
from n2v.internals.N2V_DataGenerator import N2V_DataGenerator
from astropy.io import fits
import glob
import os

def test_patch_generation():
    """测试patch生成是否正常工作"""
    
    print("=== 测试Patch生成 ===")
    
    # 获取一个测试文件
    data_path = "data"
    fits_files = glob.glob(os.path.join(data_path, "*.fit*"))
    
    if len(fits_files) == 0:
        print("错误: 没有找到任何FITS文件!")
        return
    
    test_file = fits_files[0]
    print(f"使用测试文件: {os.path.basename(test_file)}")
    
    # 加载图像
    try:
        with fits.open(test_file) as hdul:
            image = hdul[0].data.astype(np.float32)
            print(f"原始图像形状: {image.shape}")
            print(f"图像数据类型: {image.dtype}")
            print(f"图像值范围: {image.min():.2f} - {image.max():.2f}")
    except Exception as e:
        print(f"加载图像时出错: {e}")
        return
    
    # 检查图像维度
    if len(image.shape) != 2:
        print(f"错误: 图像不是2D的，实际维度: {len(image.shape)}")
        return
    
    # 添加通道维度
    image_with_channel = image[..., np.newaxis]
    print(f"添加通道维度后形状: {image_with_channel.shape}")
    
    # 测试不同的patch生成方法
    datagen = N2V_DataGenerator()
    
    print("\n=== 方法1: generate_patches ===")
    try:
        patches1 = datagen.generate_patches(image_with_channel, 
                                          shape=(64, 64), 
                                          augment=False)
        if patches1 is not None:
            print(f"方法1成功: 生成了 {len(patches1)} 个patches，形状: {patches1.shape}")
        else:
            print("方法1失败: 返回None")
    except Exception as e:
        print(f"方法1出错: {e}")
    
    print("\n=== 方法2: generate_patches_from_list ===")
    try:
        patches2 = datagen.generate_patches_from_list([image_with_channel], 
                                                    shape=(64, 64), 
                                                    augment=False)
        if patches2 is not None:
            print(f"方法2成功: 生成了 {len(patches2)} 个patches，形状: {patches2.shape}")
        else:
            print("方法2失败: 返回None")
    except Exception as e:
        print(f"方法2出错: {e}")
    
    print("\n=== 方法3: 手动patch提取 ===")
    try:
        # 手动提取patches作为备选方案
        h, w = image.shape
        patch_size = 64
        stride = 32  # 重叠提取
        
        manual_patches = []
        for y in range(0, h - patch_size + 1, stride):
            for x in range(0, w - patch_size + 1, stride):
                patch = image[y:y+patch_size, x:x+patch_size]
                patch_with_channel = patch[..., np.newaxis]
                manual_patches.append(patch_with_channel)
                
                # 限制数量
                if len(manual_patches) >= 100:
                    break
            if len(manual_patches) >= 100:
                break
        
        manual_patches = np.array(manual_patches)
        print(f"方法3成功: 手动生成了 {len(manual_patches)} 个patches，形状: {manual_patches.shape}")
        
    except Exception as e:
        print(f"方法3出错: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_patch_generation()
