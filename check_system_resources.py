import psutil
import os
import glob
import numpy as np
from astropy.io import fits
import tensorflow as tf

def check_system_resources():
    """检查系统资源和数据大小"""
    
    print("=== 系统资源检查 ===")
    
    # 检查内存
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / (1024**3):.1f} GB")
    print(f"可用内存: {memory.available / (1024**3):.1f} GB")
    print(f"内存使用率: {memory.percent:.1f}%")
    
    # 检查GPU
    print("\n=== GPU检查 ===")
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        print(f"找到 {len(gpus)} 个GPU:")
        for i, gpu in enumerate(gpus):
            print(f"  GPU {i}: {gpu.name}")
        
        try:
            gpu_details = tf.config.experimental.get_memory_info('GPU:0')
            gpu_total_mb = gpu_details['current'] / 1024 / 1024
            print(f"GPU内存使用: {gpu_total_mb:.1f} MB")
        except:
            print("无法获取GPU内存信息")
    else:
        print("未找到GPU设备")
    
    # 检查数据
    print("\n=== 数据检查 ===")
    data_path = "data"
    fits_files = glob.glob(os.path.join(data_path, "*.fit*"))
    print(f"找到 {len(fits_files)} 个FITS文件")
    
    if len(fits_files) > 0:
        # 检查第一个文件的大小
        sample_file = fits_files[0]
        file_size_mb = os.path.getsize(sample_file) / (1024**2)
        print(f"样本文件大小: {file_size_mb:.1f} MB")
        
        # 检查图像尺寸
        try:
            with fits.open(sample_file) as hdul:
                data = hdul[0].data
                print(f"图像尺寸: {data.shape}")
                print(f"数据类型: {data.dtype}")
                
                # 估算内存使用
                single_image_mb = data.nbytes / (1024**2)
                print(f"单个图像内存占用: {single_image_mb:.1f} MB")
                
                # 估算所有文件的内存占用
                total_images_mb = single_image_mb * len(fits_files)
                print(f"所有图像总内存占用: {total_images_mb:.1f} MB ({total_images_mb/1024:.1f} GB)")
                
        except Exception as e:
            print(f"读取样本文件时出错: {e}")
    
    # 内存建议
    print("\n=== 内存使用建议 ===")
    available_gb = memory.available / (1024**3)
    
    if available_gb < 4:
        print("⚠️  可用内存不足4GB，建议使用极简训练模式")
        print("   推荐参数: max_files=2, patches_per_image=30, batch_size=1")
    elif available_gb < 8:
        print("⚠️  可用内存4-8GB，建议使用保守训练模式")
        print("   推荐参数: max_files=4, patches_per_image=50, batch_size=2")
    elif available_gb < 16:
        print("✅ 可用内存8-16GB，可以使用标准优化训练模式")
        print("   推荐参数: max_files=6, patches_per_image=80, batch_size=4")
    else:
        print("✅ 可用内存充足，可以使用较大的训练参数")
        print("   推荐参数: max_files=10, patches_per_image=100, batch_size=8")

def estimate_patch_memory(num_files, patches_per_file, patch_size=64):
    """估算patch生成所需的内存"""
    total_patches = num_files * patches_per_file
    # 每个patch: 64x64x1 float32 = 16KB
    patch_memory_mb = total_patches * patch_size * patch_size * 4 / (1024**2)
    
    print(f"\n=== 内存估算 ===")
    print(f"文件数量: {num_files}")
    print(f"每文件patches: {patches_per_file}")
    print(f"总patches数量: {total_patches}")
    print(f"预估内存需求: {patch_memory_mb:.1f} MB ({patch_memory_mb/1024:.1f} GB)")
    
    available_mb = psutil.virtual_memory().available / (1024**2)
    print(f"可用内存: {available_mb:.1f} MB")
    
    if patch_memory_mb > available_mb * 0.5:  # 如果超过可用内存的50%
        print("⚠️  警告: 预估内存需求过高!")
        recommended_patches = int(available_mb * 0.3 / (patch_size * patch_size * 4 / (1024**2)))
        recommended_per_file = recommended_patches // num_files
        print(f"建议减少到每文件 {recommended_per_file} 个patches")
    else:
        print("✅ 内存需求在合理范围内")

if __name__ == "__main__":
    check_system_resources()
    
    print("\n" + "="*50)
    print("内存估算示例:")
    estimate_patch_memory(3, 50)  # 3个文件，每个50个patches
    estimate_patch_memory(6, 80)  # 6个文件，每个80个patches
    estimate_patch_memory(8, 100) # 8个文件，每个100个patches
