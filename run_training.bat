@echo off
echo ===== Noise2Void Training Script =====
echo.

echo 检查Python环境...
python --version
echo.

echo 检查必要的包...
python -c "import tensorflow as tf; print('TensorFlow版本:', tf.__version__)"
python -c "import n2v; print('Noise2Void已安装')"
python -c "import astropy; print('Astropy已安装')"
python -c "import psutil; print('psutil已安装')"
echo.

echo 创建models目录...
if not exist models mkdir models
echo.

echo ===== 选择训练模式 =====
echo 1. 标准内存优化训练 (推荐)
echo 2. 极简内存训练 (如果标准模式仍然内存不足)
echo 3. 原始训练脚本 (可能会内存溢出)
echo.
set /p choice="请选择训练模式 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 运行标准内存优化训练...
    python memory_optimized_train.py
) else if "%choice%"=="2" (
    echo.
    echo 运行极简内存训练...
    python minimal_memory_train.py
) else if "%choice%"=="3" (
    echo.
    echo 运行原始训练脚本...
    python train_noise2void.py
) else (
    echo 无效选择，默认运行标准内存优化训练...
    python memory_optimized_train.py
)

echo.
echo 训练完成！
pause
