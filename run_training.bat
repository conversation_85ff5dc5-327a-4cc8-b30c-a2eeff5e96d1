@echo off
echo ===== Noise2Void Training Script =====
echo.

echo 检查Python环境...
C:\Python\Python310\python.exe --version
echo.

echo 检查必要的包...
C:\Python\Python310\python.exe -c "import tensorflow as tf; print('TensorFlow版本:', tf.__version__)"
C:\Python\Python310\python.exe -c "import n2v; print('Noise2Void已安装')"
C:\Python\Python310\python.exe -c "import astropy; print('Astropy已安装')"
echo.

echo 创建models目录...
if not exist models mkdir models
echo.

echo ===== 选择训练模式 =====
echo 1. 手动patch训练 (推荐，避免N2V兼容性问题)
echo 2. 标准内存优化训练
echo 3. 极简内存训练
echo 4. 修复后的原始训练脚本
echo.
set /p choice="请选择训练模式 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 运行手动patch训练...
    C:\Python\Python310\python.exe manual_patch_train.py
) else if "%choice%"=="2" (
    echo.
    echo 运行标准内存优化训练...
    C:\Python\Python310\python.exe memory_optimized_train.py
) else if "%choice%"=="3" (
    echo.
    echo 运行极简内存训练...
    C:\Python\Python310\python.exe minimal_memory_train.py
) else if "%choice%"=="4" (
    echo.
    echo 运行修复后的原始训练脚本...
    C:\Python\Python310\python.exe train_noise2void.py
) else (
    echo 无效选择，默认运行手动patch训练...
    C:\Python\Python310\python.exe manual_patch_train.py
)

echo.
echo 训练完成！
pause
