import numpy as np
from n2v.models import N2V
from astropy.io import fits
import glob
import os
import matplotlib.pyplot as plt

def test_noise2void_model():
    """测试训练好的Noise2Void模型"""
    
    print("=== 测试训练好的Noise2Void模型 ===")
    
    # 加载训练好的模型
    try:
        model = N2V(config=None, name='n2v_manual_patches', basedir='models')
        print("✅ 模型加载成功!")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        print("尝试查找可用的模型...")

        # 列出可用的模型
        models_dir = "models"
        if os.path.exists(models_dir):
            subdirs = [d for d in os.listdir(models_dir) if os.path.isdir(os.path.join(models_dir, d))]
            print(f"可用的模型目录: {subdirs}")

            # 尝试使用第一个可用的模型
            for model_name in subdirs:
                config_path = os.path.join(models_dir, model_name, 'config.json')
                if os.path.exists(config_path):
                    try:
                        model = N2V(config=None, name=model_name, basedir='models')
                        print(f"✅ 成功加载模型: {model_name}")
                        break
                    except Exception as e2:
                        print(f"尝试加载 {model_name} 失败: {e2}")
                        continue
            else:
                print("❌ 没有找到可用的模型")
                return
        else:
            print("❌ models目录不存在")
            return
    
    # 获取测试图像 - 使用real_data目录
    data_path = "real_data"
    fits_files = glob.glob(os.path.join(data_path, "*.fit*"))

    if len(fits_files) == 0:
        print(f"❌ 在 {data_path} 目录中没有找到测试图像")
        print("请确保 real_data 目录存在并包含FITS文件")
        return

    # 选择第一个图像进行测试
    test_file = fits_files[0]
    print(f"使用测试文件: {os.path.basename(test_file)}")
    print(f"从 {data_path} 目录加载（真实测试数据）")
    
    # 加载测试图像
    try:
        with fits.open(test_file) as hdul:
            test_image = hdul[0].data.astype(np.float32)
            print(f"测试图像形状: {test_image.shape}")
    except Exception as e:
        print(f"❌ 加载测试图像失败: {e}")
        return
    
    # 选择一个小区域进行测试（避免内存问题）
    h, w = test_image.shape
    crop_size = 512  # 选择512x512的区域
    
    # 从中心裁剪
    start_h = (h - crop_size) // 2
    start_w = (w - crop_size) // 2
    test_crop = test_image[start_h:start_h+crop_size, start_w:start_w+crop_size]
    
    print(f"测试区域形状: {test_crop.shape}")
    print(f"测试区域数值范围: {test_crop.min():.2f} - {test_crop.max():.2f}")
    
    # 进行预测
    print("开始去噪预测...")
    try:
        # 预测需要添加通道维度
        test_input = test_crop[..., np.newaxis]
        denoised = model.predict(test_input, axes='YXC')
        print("✅ 预测完成!")
        print(f"去噪结果形状: {denoised.shape}")
        print(f"去噪结果数值范围: {denoised.min():.2f} - {denoised.max():.2f}")
        
    except Exception as e:
        print(f"❌ 预测失败: {e}")
        return
    
    # 保存结果
    print("保存测试结果...")
    
    # 创建结果目录
    os.makedirs('test_results', exist_ok=True)
    
    # 保存原始图像
    original_fits = fits.PrimaryHDU(test_crop)
    original_fits.writeto('test_results/original_crop.fits', overwrite=True)
    
    # 保存去噪结果（移除通道维度）
    denoised_2d = denoised[..., 0] if len(denoised.shape) == 3 else denoised
    denoised_fits = fits.PrimaryHDU(denoised_2d)
    denoised_fits.writeto('test_results/denoised_crop.fits', overwrite=True)
    
    print("✅ 结果已保存到 test_results/ 目录")
    print("- original_crop.fits: 原始图像")
    print("- denoised_crop.fits: 去噪后图像")
    
    # 创建对比图
    try:
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 3, 1)
        plt.imshow(test_crop, cmap='gray')
        plt.title('原始图像')
        plt.colorbar()
        
        plt.subplot(1, 3, 2)
        plt.imshow(denoised_2d, cmap='gray')
        plt.title('去噪后图像')
        plt.colorbar()
        
        plt.subplot(1, 3, 3)
        difference = test_crop - denoised_2d
        plt.imshow(difference, cmap='RdBu_r')
        plt.title('差异图 (原始-去噪)')
        plt.colorbar()
        
        plt.tight_layout()
        plt.savefig('test_results/comparison.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✅ 对比图已保存到 test_results/comparison.png")
        
    except Exception as e:
        print(f"⚠️  创建对比图时出错: {e}")
    
    # 计算一些统计信息
    print("\n=== 去噪效果统计 ===")
    noise_reduction = np.std(test_crop) - np.std(denoised_2d)
    print(f"噪声标准差减少: {noise_reduction:.4f}")
    print(f"原始图像标准差: {np.std(test_crop):.4f}")
    print(f"去噪后标准差: {np.std(denoised_2d):.4f}")
    
    # 计算PSNR
    mse = np.mean((test_crop - denoised_2d) ** 2)
    if mse > 0:
        psnr = 20 * np.log10(np.max(test_crop) / np.sqrt(mse))
        print(f"PSNR: {psnr:.2f} dB")
    
    print("\n🎉 测试完成！模型工作正常。")

if __name__ == "__main__":
    test_noise2void_model()
