import numpy as np
from n2v.models import N2V
from astropy.io import fits
import glob
import os
import gc
from tqdm import tqdm

def denoise_large_image(model, image, patch_size=512, overlap=64):
    """
    对大图像进行分块去噪处理
    
    参数:
    - model: 训练好的N2V模型
    - image: 输入图像 (2D numpy array)
    - patch_size: 处理块的大小
    - overlap: 块之间的重叠像素数
    """
    
    h, w = image.shape
    stride = patch_size - overlap
    
    # 计算需要的块数
    num_h = (h - overlap) // stride + (1 if (h - overlap) % stride > 0 else 0)
    num_w = (w - overlap) // stride + (1 if (w - overlap) % stride > 0 else 0)
    
    print(f"图像尺寸: {h}x{w}")
    print(f"将分成 {num_h}x{num_w} = {num_h * num_w} 个块进行处理")
    
    # 初始化结果图像和权重图像
    result = np.zeros_like(image, dtype=np.float32)
    weights = np.zeros_like(image, dtype=np.float32)
    
    # 创建权重模板（中心权重高，边缘权重低）
    weight_template = np.ones((patch_size, patch_size), dtype=np.float32)
    if overlap > 0:
        # 创建渐变权重
        fade = overlap // 2
        for i in range(fade):
            weight = (i + 1) / fade
            weight_template[i, :] *= weight
            weight_template[-(i+1), :] *= weight
            weight_template[:, i] *= weight
            weight_template[:, -(i+1)] *= weight
    
    # 逐块处理
    with tqdm(total=num_h * num_w, desc="处理进度") as pbar:
        for i in range(num_h):
            for j in range(num_w):
                # 计算块的位置
                start_h = i * stride
                end_h = min(start_h + patch_size, h)
                start_w = j * stride
                end_w = min(start_w + patch_size, w)
                
                # 提取块
                patch = image[start_h:end_h, start_w:end_w]
                
                # 如果块小于预期大小，进行填充
                if patch.shape[0] < patch_size or patch.shape[1] < patch_size:
                    padded_patch = np.zeros((patch_size, patch_size), dtype=patch.dtype)
                    padded_patch[:patch.shape[0], :patch.shape[1]] = patch
                    patch = padded_patch
                
                # 添加通道维度并预测
                patch_input = patch[..., np.newaxis]
                try:
                    denoised_patch = model.predict(patch_input, axes='YXC', verbose=0)
                    denoised_patch = denoised_patch[..., 0]  # 移除通道维度
                except Exception as e:
                    print(f"处理块 ({i},{j}) 时出错: {e}")
                    denoised_patch = patch
                
                # 裁剪到实际大小
                actual_h = end_h - start_h
                actual_w = end_w - start_w
                denoised_patch = denoised_patch[:actual_h, :actual_w]
                current_weights = weight_template[:actual_h, :actual_w]
                
                # 累加到结果中
                result[start_h:end_h, start_w:end_w] += denoised_patch * current_weights
                weights[start_h:end_h, start_w:end_w] += current_weights
                
                pbar.update(1)
                
                # 清理内存
                del patch, patch_input, denoised_patch
                gc.collect()
    
    # 归一化重叠区域
    weights[weights == 0] = 1  # 避免除零
    result = result / weights
    
    return result.astype(image.dtype)

def batch_denoise_fits():
    """批量处理FITS文件"""
    
    print("=== Noise2Void 批量去噪处理 ===")
    
    # 加载模型
    print("加载训练好的模型...")
    try:
        model = N2V(config=None, name='n2v_manual_patches', basedir='models')
        print("✅ 模型加载成功!")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 获取输入文件
    input_dir = "data"
    output_dir = "denoised_results"
    
    fits_files = glob.glob(os.path.join(input_dir, "*.fit*"))
    if len(fits_files) == 0:
        print(f"❌ 在 {input_dir} 中没有找到FITS文件")
        return
    
    print(f"找到 {len(fits_files)} 个FITS文件")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 处理每个文件
    for i, input_file in enumerate(fits_files):
        print(f"\n处理文件 {i+1}/{len(fits_files)}: {os.path.basename(input_file)}")
        
        try:
            # 加载图像
            with fits.open(input_file) as hdul:
                original_image = hdul[0].data.astype(np.float32)
                header = hdul[0].header
            
            print(f"图像形状: {original_image.shape}")
            print(f"数值范围: {original_image.min():.1f} - {original_image.max():.1f}")
            
            # 去噪处理
            print("开始去噪处理...")
            denoised_image = denoise_large_image(model, original_image, 
                                               patch_size=512, overlap=64)
            
            print(f"去噪完成，结果范围: {denoised_image.min():.1f} - {denoised_image.max():.1f}")
            
            # 保存结果
            output_filename = f"denoised_{os.path.basename(input_file)}"
            output_path = os.path.join(output_dir, output_filename)
            
            # 创建新的FITS文件
            hdu = fits.PrimaryHDU(denoised_image, header=header)
            hdu.header['HISTORY'] = 'Processed with Noise2Void denoising'
            hdu.header['N2V_DATE'] = '2025-08-14'
            hdu.writeto(output_path, overwrite=True)
            
            print(f"✅ 结果已保存到: {output_path}")
            
            # 计算统计信息
            noise_reduction = np.std(original_image) - np.std(denoised_image)
            print(f"噪声标准差减少: {noise_reduction:.2f}")
            
        except Exception as e:
            print(f"❌ 处理文件 {input_file} 时出错: {e}")
            continue
        
        # 清理内存
        del original_image, denoised_image
        gc.collect()
    
    print(f"\n🎉 批量处理完成！结果保存在 {output_dir} 目录中。")

if __name__ == "__main__":
    batch_denoise_fits()
