import numpy as np
from n2v.models import N2VConfig, N2V
from n2v.utils.n2v_utils import manipulate_val_data
from n2v.internals.N2V_DataGenerator import N2V_DataGenerator
from astropy.io import fits
import glob
import os
import tensorflow as tf
import gc
import psutil
import time

def print_memory_usage(stage=""):
    """打印当前内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    memory_mb = memory_info.rss / 1024 / 1024
    print(f"[{stage}] 当前内存使用: {memory_mb:.1f} MB")
    
    # 如果有GPU，也打印GPU内存使用
    try:
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            gpu_details = tf.config.experimental.get_memory_info('GPU:0')
            gpu_used_mb = gpu_details['current'] / 1024 / 1024
            print(f"[{stage}] GPU内存使用: {gpu_used_mb:.1f} MB")
    except:
        pass

# 检查CUDA是否可用
has_cuda = tf.test.is_built_with_cuda()
print(f"TensorFlow是否构建了CUDA支持: {has_cuda}")

# 强制使用GPU (如果可用)
if has_cuda:
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"成功找到并配置了{len(gpus)}个GPU")
            print(f"GPU设备名称: {[gpu.name for gpu in gpus]}")
        except RuntimeError as e:
            print(f"GPU配置错误: {e}")
    else:
        print("未找到GPU设备，但TensorFlow支持CUDA。请检查NVIDIA驱动和CUDA安装。")
else:
    print("TensorFlow未构建CUDA支持。请安装支持CUDA的TensorFlow版本。")

print_memory_usage("初始化后")

def load_single_fits_image(file_path):
    """加载单个FITS图像"""
    try:
        with fits.open(file_path) as hdul:
            data = hdul[0].data.astype(np.float32)
            return data
    except Exception as e:
        print(f"加载文件 {file_path} 时出错: {e}")
        return None

def generate_patches_from_single_image(image, patch_shape=(64, 64), max_patches=50):
    """从单个图像生成patches"""
    if image is None:
        return []
    
    # 添加通道维度
    img_with_channel = image[..., np.newaxis]
    
    # 生成patches
    datagen = N2V_DataGenerator()
    patches = datagen.generate_patches(img_with_channel, 
                                     shape=patch_shape, 
                                     augment=False)
    
    # 限制patch数量
    if len(patches) > max_patches:
        indices = np.random.choice(len(patches), max_patches, replace=False)
        patches = patches[indices]
    
    return patches

def train_noise2void_memory_efficient():
    """内存优化的Noise2Void训练"""
    
    data_path = "data"
    fits_files = glob.glob(os.path.join(data_path, "*.fit*"))
    
    if len(fits_files) == 0:
        raise ValueError("没有找到任何FITS文件!")
    
    print(f"找到 {len(fits_files)} 个FITS文件")
    
    # 限制使用的文件数量
    max_files = min(6, len(fits_files))  # 最多使用6个文件
    fits_files = fits_files[:max_files]
    print(f"将使用 {len(fits_files)} 个文件进行训练")
    
    # 逐个处理图像，生成patches
    all_patches = []
    patches_per_image = 80  # 每个图像生成80个patches
    
    for i, file_path in enumerate(fits_files):
        print(f"\n处理文件 {i+1}/{len(fits_files)}: {os.path.basename(file_path)}")
        print_memory_usage(f"处理文件{i+1}前")
        
        # 加载单个图像
        image = load_single_fits_image(file_path)
        if image is not None:
            print(f"图像形状: {image.shape}")
            
            # 生成patches
            patches = generate_patches_from_single_image(image, 
                                                       patch_shape=(64, 64), 
                                                       max_patches=patches_per_image)
            
            if len(patches) > 0:
                all_patches.extend(patches)
                print(f"生成了 {len(patches)} 个patches")
            
            # 清理内存
            del image, patches
            gc.collect()
            
        print_memory_usage(f"处理文件{i+1}后")
    
    if len(all_patches) == 0:
        raise ValueError("没有生成任何训练patches!")
    
    # 转换为numpy数组
    print(f"\n总共生成了 {len(all_patches)} 个patches")
    patches_array = np.array(all_patches)
    print(f"Patches数组形状: {patches_array.shape}")
    
    # 清理列表
    del all_patches
    gc.collect()
    print_memory_usage("patches生成完成后")
    
    # 分割训练和验证数据
    split_idx = int(0.9 * len(patches_array))
    X_train = patches_array[:split_idx]
    X_val = patches_array[split_idx:]
    
    print(f"训练数据形状: {X_train.shape}")
    print(f"验证数据形状: {X_val.shape}")
    
    # 清理原始patches数组
    del patches_array
    gc.collect()
    print_memory_usage("数据分割完成后")
    
    # 配置模型 - 极度保守的内存设置
    print("\n=== 配置Noise2Void模型 ===")
    config = N2VConfig(X_train,
                       unet_kern_size=3,
                       train_steps_per_epoch=30,   # 进一步减少步数
                       train_epochs=30,            # 减少训练轮数
                       train_loss='mse',
                       batch_size=4,               # 极小的批量大小
                       train_batch_size=4,
                       n2v_perc_pix=0.198,
                       n2v_patch_shape=(64, 64),
                       n2v_manipulator='uniform_withCP',
                       n2v_neighborhood_radius=5,
                       train_checkpoint='weights_best.weights.h5',
                       train_learning_rate=0.0008)  # 提高学习率以补偿小批量
    
    print("模型配置:")
    print(f"- 训练数据数量: {len(X_train)}")
    print(f"- 验证数据数量: {len(X_val)}")
    print(f"- 批量大小: {config.batch_size}")
    print(f"- 每轮步数: {config.train_steps_per_epoch}")
    print(f"- 训练轮数: {config.train_epochs}")
    
    # 创建模型
    print("\n=== 创建模型 ===")
    model = N2V(config, 'n2v_astro_ultra_optimized', basedir='models')
    print_memory_usage("模型创建后")
    
    # 训练模型
    print("\n=== 开始训练模型 ===")
    try:
        start_time = time.time()
        history = model.train(X_train, X_val)
        end_time = time.time()
        
        print(f"模型训练完成! 耗时: {(end_time - start_time)/60:.1f} 分钟")
        
        # 保存训练历史
        os.makedirs('models', exist_ok=True)
        np.save('models/training_history_optimized.npy', history.history)
        print("训练历史已保存到 models/training_history_optimized.npy")
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        print("如果仍然出现内存错误，请尝试:")
        print("1. 进一步减少 max_files 参数")
        print("2. 减少 patches_per_image 参数")
        print("3. 将 batch_size 设置为 2 或 1")
        
    finally:
        # 清理内存
        try:
            del X_train, X_val
        except:
            pass
        gc.collect()
        print_memory_usage("训练完成后")

if __name__ == "__main__":
    print("=== 启动内存优化的Noise2Void训练 ===")
    print_memory_usage("开始")
    train_noise2void_memory_efficient()
