import sys
import os

def check_environment():
    """检查Python环境和必要的包"""
    
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查必要的包
    required_packages = [
        'numpy',
        'tensorflow', 
        'astropy',
        'n2v',
        'psutil'
    ]
    
    print("\n=== 包检查 ===")
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下包: {', '.join(missing_packages)}")
        print("\n安装命令:")
        if 'n2v' in missing_packages:
            print("pip install n2v[tensorflow]")
        if 'astropy' in missing_packages:
            print("pip install astropy")
        if 'psutil' in missing_packages:
            print("pip install psutil")
    else:
        print("\n✅ 所有必要的包都已安装!")
    
    # 检查数据文件
    print("\n=== 数据文件检查 ===")
    data_path = "data"
    if os.path.exists(data_path):
        import glob
        fits_files = glob.glob(os.path.join(data_path, "*.fit*"))
        print(f"找到 {len(fits_files)} 个FITS文件")
        if len(fits_files) > 0:
            print(f"第一个文件: {os.path.basename(fits_files[0])}")
    else:
        print("❌ data目录不存在")

if __name__ == "__main__":
    check_environment()
