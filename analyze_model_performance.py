import numpy as np
from n2v.models import N2V
from astropy.io import fits
import glob
import os
import matplotlib.pyplot as plt

def analyze_data_distribution(data_dir, title):
    """分析数据分布"""
    fits_files = glob.glob(os.path.join(data_dir, "*.fit*"))
    if len(fits_files) == 0:
        print(f"❌ {data_dir} 目录中没有找到文件")
        return None
    
    print(f"\n=== {title} 数据分析 ===")
    print(f"文件数量: {len(fits_files)}")
    
    all_stats = []
    for i, file_path in enumerate(fits_files[:3]):  # 只分析前3个文件
        with fits.open(file_path) as hdul:
            data = hdul[0].data.astype(np.float32)
            
        stats = {
            'file': os.path.basename(file_path),
            'shape': data.shape,
            'min': data.min(),
            'max': data.max(),
            'mean': data.mean(),
            'std': data.std(),
            'median': np.median(data)
        }
        all_stats.append(stats)
        
        print(f"文件 {i+1}: {stats['file']}")
        print(f"  形状: {stats['shape']}")
        print(f"  范围: {stats['min']:.1f} - {stats['max']:.1f}")
        print(f"  均值: {stats['mean']:.1f}, 标准差: {stats['std']:.1f}")
        print(f"  中位数: {stats['median']:.1f}")
    
    return all_stats

def test_model_on_both_datasets():
    """在训练数据和真实数据上测试模型"""
    
    print("=== Noise2Void 模型性能分析 ===")
    
    # 加载模型
    try:
        model = N2V(config=None, name='n2v_manual_patches', basedir='models')
        print("✅ 模型加载成功!")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 分析训练数据和真实数据的分布
    train_stats = analyze_data_distribution("data", "训练数据")
    real_stats = analyze_data_distribution("real_data", "真实数据")
    
    if train_stats is None or real_stats is None:
        return
    
    # 对比测试
    datasets = [
        ("data", "训练数据", train_stats[0]),
        ("real_data", "真实数据", real_stats[0])
    ]
    
    results = []
    
    for data_dir, dataset_name, stats in datasets:
        print(f"\n=== 测试 {dataset_name} ===")
        
        # 加载第一个文件
        fits_files = glob.glob(os.path.join(data_dir, "*.fit*"))
        test_file = fits_files[0]
        
        with fits.open(test_file) as hdul:
            test_image = hdul[0].data.astype(np.float32)
        
        # 选择中心区域
        h, w = test_image.shape
        crop_size = 512
        start_h = (h - crop_size) // 2
        start_w = (w - crop_size) // 2
        test_crop = test_image[start_h:start_h+crop_size, start_w:start_w+crop_size]
        
        print(f"测试区域统计:")
        print(f"  范围: {test_crop.min():.1f} - {test_crop.max():.1f}")
        print(f"  均值: {test_crop.mean():.1f}, 标准差: {test_crop.std():.1f}")
        
        # 预测
        test_input = test_crop[..., np.newaxis]
        try:
            denoised = model.predict(test_input, axes='YXC')
            denoised_2d = denoised[..., 0]
            
            # 计算统计信息
            psnr = 20 * np.log10(np.max(test_crop) / np.sqrt(np.mean((test_crop - denoised_2d) ** 2)))
            noise_reduction = np.std(test_crop) - np.std(denoised_2d)
            
            result = {
                'dataset': dataset_name,
                'original_range': (test_crop.min(), test_crop.max()),
                'denoised_range': (denoised_2d.min(), denoised_2d.max()),
                'original_std': np.std(test_crop),
                'denoised_std': np.std(denoised_2d),
                'noise_reduction': noise_reduction,
                'psnr': psnr
            }
            results.append(result)
            
            print(f"去噪结果:")
            print(f"  范围: {denoised_2d.min():.1f} - {denoised_2d.max():.1f}")
            print(f"  PSNR: {psnr:.2f} dB")
            print(f"  噪声减少: {noise_reduction:.2f}")
            
        except Exception as e:
            print(f"❌ 预测失败: {e}")
    
    # 生成对比报告
    print("\n" + "="*60)
    print("📊 模型性能对比报告")
    print("="*60)
    
    for result in results:
        print(f"\n{result['dataset']}:")
        print(f"  PSNR: {result['psnr']:.2f} dB")
        print(f"  噪声减少: {result['noise_reduction']:.2f}")
        print(f"  原始标准差: {result['original_std']:.2f}")
        print(f"  去噪后标准差: {result['denoised_std']:.2f}")
    
    # 分析问题
    print("\n📋 问题分析:")
    if len(results) >= 2:
        train_result = results[0]
        real_result = results[1]
        
        psnr_diff = train_result['psnr'] - real_result['psnr']
        print(f"1. PSNR差异: {psnr_diff:.2f} dB")
        
        if psnr_diff > 10:
            print("   ⚠️  真实数据上的性能显著下降，可能原因:")
            print("   - 训练数据与真实数据分布差异较大")
            print("   - 模型过拟合到训练数据")
            print("   - 需要更多样化的训练数据")
        
        if real_result['noise_reduction'] < 0:
            print("2. ⚠️  真实数据上噪声增加，可能原因:")
            print("   - 模型引入了伪影")
            print("   - 数据预处理不当")
            print("   - 模型训练不充分")
    
    print("\n💡 改进建议:")
    print("1. 使用更多样化的训练数据")
    print("2. 增加数据增强")
    print("3. 调整模型参数")
    print("4. 延长训练时间")
    print("5. 使用迁移学习或微调")

def create_comparison_plots():
    """创建详细的对比图"""
    try:
        model = N2V(config=None, name='n2v_manual_patches', basedir='models')
    except:
        print("❌ 无法加载模型")
        return
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    datasets = [("data", "Training Data"), ("real_data", "Real Data")]
    
    for row, (data_dir, title) in enumerate(datasets):
        fits_files = glob.glob(os.path.join(data_dir, "*.fit*"))
        if len(fits_files) == 0:
            continue
            
        with fits.open(fits_files[0]) as hdul:
            image = hdul[0].data.astype(np.float32)
        
        # 裁剪中心区域
        h, w = image.shape
        crop_size = 512
        start_h = (h - crop_size) // 2
        start_w = (w - crop_size) // 2
        crop = image[start_h:start_h+crop_size, start_w:start_w+crop_size]
        
        # 去噪
        crop_input = crop[..., np.newaxis]
        denoised = model.predict(crop_input, axes='YXC')[..., 0]
        
        # 绘图
        axes[row, 0].imshow(crop, cmap='gray', vmin=np.percentile(crop, 1), vmax=np.percentile(crop, 99))
        axes[row, 0].set_title(f'{title} - Original')
        axes[row, 0].axis('off')
        
        axes[row, 1].imshow(denoised, cmap='gray', vmin=np.percentile(denoised, 1), vmax=np.percentile(denoised, 99))
        axes[row, 1].set_title(f'{title} - Denoised')
        axes[row, 1].axis('off')
        
        diff = crop - denoised
        axes[row, 2].imshow(diff, cmap='RdBu_r', vmin=np.percentile(diff, 5), vmax=np.percentile(diff, 95))
        axes[row, 2].set_title(f'{title} - Difference')
        axes[row, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('model_performance_comparison.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("✅ 对比图已保存到 model_performance_comparison.png")

if __name__ == "__main__":
    test_model_on_both_datasets()
    create_comparison_plots()
