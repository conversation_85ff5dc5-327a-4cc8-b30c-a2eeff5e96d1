import numpy as np
from n2v.models import N2VConfig, N2V
from astropy.io import fits
import glob
import os
import tensorflow as tf
import gc
import psutil

def print_memory_usage(stage=""):
    """打印当前内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    memory_mb = memory_info.rss / 1024 / 1024
    print(f"[{stage}] 内存使用: {memory_mb:.1f} MB")

# GPU配置
print("=== GPU配置 ===")
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"配置了{len(gpus)}个GPU")
    except RuntimeError as e:
        print(f"GPU配置错误: {e}")

def extract_patches_manually(image, patch_size=64, stride=32, max_patches=50):
    """手动提取patches，避免N2V_DataGenerator的问题"""
    
    if len(image.shape) != 2:
        print(f"错误: 图像不是2D的，形状: {image.shape}")
        return []
    
    h, w = image.shape
    patches = []
    
    # 计算可以提取的patch位置
    y_positions = list(range(0, h - patch_size + 1, stride))
    x_positions = list(range(0, w - patch_size + 1, stride))
    
    print(f"图像尺寸: {h}x{w}")
    print(f"可提取的Y位置数量: {len(y_positions)}")
    print(f"可提取的X位置数量: {len(x_positions)}")
    print(f"理论最大patch数量: {len(y_positions) * len(x_positions)}")
    
    # 随机选择位置以限制patch数量
    total_possible = len(y_positions) * len(x_positions)
    if total_possible > max_patches:
        # 随机选择位置
        all_positions = [(y, x) for y in y_positions for x in x_positions]
        selected_positions = np.random.choice(len(all_positions), max_patches, replace=False)
        selected_positions = [all_positions[i] for i in selected_positions]
    else:
        selected_positions = [(y, x) for y in y_positions for x in x_positions]
    
    print(f"实际选择的patch数量: {len(selected_positions)}")
    
    # 提取patches
    for y, x in selected_positions:
        patch = image[y:y+patch_size, x:x+patch_size]
        # 添加通道维度
        patch_with_channel = patch[..., np.newaxis]
        patches.append(patch_with_channel)
    
    return np.array(patches)

def train_with_manual_patches():
    """使用手动patch提取的训练方案"""
    
    print("=== 手动Patch提取训练 ===")
    print_memory_usage("开始")
    
    # 获取FITS文件
    data_path = "data"
    fits_files = glob.glob(os.path.join(data_path, "*.fit*"))
    
    if len(fits_files) == 0:
        raise ValueError("没有找到任何FITS文件!")
    
    # 限制文件数量
    max_files = 4  # 使用4个文件
    fits_files = fits_files[:max_files]
    print(f"将使用 {len(fits_files)} 个文件进行训练")
    
    # 逐个处理图像
    all_patches = []
    patches_per_image = 60  # 每个图像60个patches
    
    for i, file_path in enumerate(fits_files):
        print(f"\n处理文件 {i+1}/{len(fits_files)}: {os.path.basename(file_path)}")
        print_memory_usage(f"处理文件{i+1}前")
        
        try:
            # 加载图像
            with fits.open(file_path) as hdul:
                image = hdul[0].data.astype(np.float32)
                print(f"图像形状: {image.shape}")
            
            # 手动提取patches
            patches = extract_patches_manually(image, 
                                             patch_size=64, 
                                             stride=32, 
                                             max_patches=patches_per_image)
            
            if len(patches) > 0:
                all_patches.extend(patches)
                print(f"成功提取了 {len(patches)} 个patches")
            else:
                print(f"图像 {i+1} 没有提取到任何patches")
            
            # 清理内存
            del image, patches
            gc.collect()
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue
            
        print_memory_usage(f"文件{i+1}处理完成")
    
    if len(all_patches) == 0:
        raise ValueError("没有提取到任何patches!")
    
    # 转换为numpy数组
    print(f"\n总共提取了 {len(all_patches)} 个patches")
    patches_array = np.array(all_patches)
    print(f"Patches数组形状: {patches_array.shape}")
    
    # 清理列表
    del all_patches
    gc.collect()
    print_memory_usage("patches生成完成")
    
    # 分割训练和验证数据
    split_idx = int(0.8 * len(patches_array))
    X_train = patches_array[:split_idx]
    X_val = patches_array[split_idx:]
    
    print(f"训练数据形状: {X_train.shape}")
    print(f"验证数据形状: {X_val.shape}")
    
    # 清理原始数组
    del patches_array
    gc.collect()
    print_memory_usage("数据分割完成")
    
    # 配置模型
    print("\n=== 配置模型 ===")
    config = N2VConfig(X_train,
                       unet_kern_size=3,
                       train_steps_per_epoch=20,
                       train_epochs=30,
                       train_loss='mse',
                       batch_size=4,
                       train_batch_size=4,
                       n2v_perc_pix=0.198,
                       n2v_patch_shape=(64, 64),
                       n2v_manipulator='uniform_withCP',
                       n2v_neighborhood_radius=5,
                       train_checkpoint='weights_manual.weights.h5',
                       train_learning_rate=0.0008)
    
    print("模型配置:")
    print(f"- 训练数据数量: {len(X_train)}")
    print(f"- 验证数据数量: {len(X_val)}")
    print(f"- 批量大小: {config.batch_size}")
    
    # 创建模型
    print("\n=== 创建模型 ===")
    model = N2V(config, 'n2v_manual_patches', basedir='models')
    print_memory_usage("模型创建完成")
    
    # 训练
    print("\n=== 开始训练 ===")
    try:
        history = model.train(X_train, X_val)
        print("训练完成!")
        
        # 保存历史
        os.makedirs('models', exist_ok=True)
        np.save('models/manual_training_history.npy', history.history)
        print("训练历史已保存")
        
    except Exception as e:
        print(f"训练错误: {e}")
        
    finally:
        try:
            del X_train, X_val
        except:
            pass
        gc.collect()
        print_memory_usage("训练完成")

if __name__ == "__main__":
    train_with_manual_patches()
