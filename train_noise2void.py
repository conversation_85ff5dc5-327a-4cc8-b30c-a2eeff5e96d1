import numpy as np
from n2v.models import N2VConfig, N2V
from n2v.utils.n2v_utils import manipulate_val_data
from n2v.internals.N2V_DataGenerator import N2V_DataGenerator
from astropy.io import fits
import glob
import os
import tensorflow as tf
import gc  # 添加垃圾回收模块

# 检查CUDA是否可用
has_cuda = tf.test.is_built_with_cuda()
print(f"TensorFlow是否构建了CUDA支持: {has_cuda}")

# 强制使用GPU (如果可用)
if has_cuda:
    # 设置环境变量以优先使用GPU
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    
    # 尝试配置GPU内存增长
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"成功找到并配置了{len(gpus)}个GPU")
            print(f"GPU设备名称: {[gpu.name for gpu in gpus]}")
        except RuntimeError as e:
            print(f"GPU配置错误: {e}")
    else:
        print("未找到GPU设备，但TensorFlow支持CUDA。请检查NVIDIA驱动和CUDA安装。")
else:
    print("TensorFlow未构建CUDA支持。请安装支持CUDA的TensorFlow版本。")

# 创建会话时指定GPU
config = tf.compat.v1.ConfigProto()
config.gpu_options.allow_growth = True
config.gpu_options.per_process_gpu_memory_fraction = 0.8  # 限制GPU内存使用比例
# 尝试创建会话
try:
    sess = tf.compat.v1.Session(config=config)
    print("成功创建GPU会话")
except Exception as e:
    print(f"创建GPU会话时出错: {e}")

# 内存优化的FITS图像加载函数
def load_fits_images_batch(data_path, max_images=10):
    """
    批量加载FITS图像以避免内存溢出
    max_images: 一次最多加载的图像数量
    """
    abs_data_path = os.path.abspath(data_path)
    print(f"正在从路径加载FITS图像: {abs_data_path}")

    fits_files = glob.glob(os.path.join(data_path, "*.fit*"))
    print(f"找到的FITS文件数量: {len(fits_files)}")

    if len(fits_files) == 0:
        print("警告: 没有找到任何FITS文件!")
        return []

    # 限制加载的文件数量以节省内存
    fits_files = fits_files[:max_images]
    print(f"实际加载的文件数量: {len(fits_files)}")

    images = []
    for i, file in enumerate(fits_files):
        try:
            with fits.open(file) as hdul:
                data = hdul[0].data.astype(np.float32)
                images.append(data)
                print(f"成功加载文件 {i+1}/{len(fits_files)}: {os.path.basename(file)}, 形状: {data.shape}")
        except Exception as e:
            print(f"加载文件 {file} 时出错: {e}")

    if len(images) == 0:
        print("警告: 没有成功加载任何图像数据!")
        return []

    return images

# 手动patch提取函数（避免N2V_DataGenerator的问题）
def generate_patches_manually(images, patch_size=64, stride=32, patches_per_image=50):
    """
    手动提取patches，避免N2V_DataGenerator的兼容性问题
    """
    print(f"开始手动生成patches，每个图像最多生成 {patches_per_image} 个patches")

    all_patches = []

    for i, img in enumerate(images):
        print(f"处理图像 {i+1}/{len(images)}, 原始形状: {img.shape}")

        # 确保图像是2D的
        if len(img.shape) != 2:
            print(f"警告: 图像维度不是2D，跳过此图像")
            continue

        h, w = img.shape

        # 计算可以提取的patch位置
        y_positions = list(range(0, h - patch_size + 1, stride))
        x_positions = list(range(0, w - patch_size + 1, stride))

        print(f"图像尺寸: {h}x{w}")
        print(f"可提取patch位置: Y={len(y_positions)}, X={len(x_positions)}")

        # 生成所有可能的位置组合
        all_positions = [(y, x) for y in y_positions for x in x_positions]
        total_possible = len(all_positions)
        print(f"理论最大patch数量: {total_possible}")

        # 随机选择位置以限制patch数量
        if total_possible > patches_per_image:
            selected_indices = np.random.choice(total_possible, patches_per_image, replace=False)
            selected_positions = [all_positions[i] for i in selected_indices]
        else:
            selected_positions = all_positions

        print(f"实际选择的patch数量: {len(selected_positions)}")

        # 提取patches
        image_patches = []
        for y, x in selected_positions:
            patch = img[y:y+patch_size, x:x+patch_size]
            # 添加通道维度: (H, W) -> (H, W, C)
            patch_with_channel = patch[..., np.newaxis]
            image_patches.append(patch_with_channel)

        all_patches.extend(image_patches)
        print(f"图像 {i+1} 成功提取了 {len(image_patches)} 个patches")

        # 清理内存
        del image_patches
        gc.collect()

    if len(all_patches) == 0:
        raise ValueError("没有提取到任何有效的patches！")

    print(f"总共提取了 {len(all_patches)} 个patches")
    patches_array = np.array(all_patches)
    print(f"最终patches数组形状: {patches_array.shape}")
    return patches_array

# 主要执行代码
if __name__ == "__main__":
    # 数据路径和内存优化的数据加载
    data_path = "data"

    # 内存优化：只加载部分图像
    print("=== 内存优化的数据加载 ===")
    images = load_fits_images_batch(data_path, max_images=8)  # 限制加载8个图像

    # 检查数据是否加载成功
    if len(images) == 0:
        raise ValueError("没有成功加载任何图像数据。请检查数据路径和文件格式。")

    print(f"成功加载的图像数量: {len(images)}")
    print(f"第一个图像的形状: {images[0].shape}")

    # 手动patch生成（避免N2V_DataGenerator的问题）
    print("=== 开始生成训练patches ===")
    patches = generate_patches_manually(images,
                                      patch_size=64,
                                      stride=32,
                                      patches_per_image=80)  # 每个图像生成80个patches

    print(f"生成的patches总数: {len(patches)}")
    print(f"Patches形状: {patches.shape}")

    # 清理原始图像数据以释放内存
    del images
    gc.collect()

    # 分割训练和验证数据
    split_idx = int(0.9 * len(patches))
    X_train = patches[:split_idx]
    X_val = patches[split_idx:]

    print(f"训练数据形状: {X_train.shape}")
    print(f"验证数据形状: {X_val.shape}")

    # 清理patches数组以释放内存
    del patches
    gc.collect()

    # 配置模型 - 针对RTX 3050和内存限制优化
    print("=== 配置Noise2Void模型 ===")
    config = N2VConfig(X_train,
                       unet_kern_size=3,
                       train_steps_per_epoch=50,   # 大幅减少步数以适应较少的数据
                       train_epochs=50,            # 减少训练轮数
                       train_loss='mse',
                       batch_size=8,               # 进一步减小批量大小以适应RTX 3050显存
                       train_batch_size=8,
                       n2v_perc_pix=0.198,
                       n2v_patch_shape=(64, 64),
                       n2v_manipulator='uniform_withCP',
                       n2v_neighborhood_radius=5,
                       train_checkpoint='weights_best.weights.h5',
                       train_learning_rate=0.0004)  # 适当提高学习率以补偿较小的批量大小

    print("模型配置完成:")
    print(f"- 训练数据数量: {len(X_train)}")
    print(f"- 验证数据数量: {len(X_val)}")
    print(f"- 批量大小: {config.batch_size}")
    print(f"- 每轮步数: {config.train_steps_per_epoch}")
    print(f"- 训练轮数: {config.train_epochs}")

    # 创建和训练模型
    print("=== 创建模型 ===")
    model = N2V(config, 'n2v_astronomical_memory_optimized', basedir='models')

    print("=== 开始训练模型 ===")
    try:
        history = model.train(X_train, X_val)
        print("模型训练完成!")

        # 保存训练历史
        np.save('models/training_history.npy', history.history)
        print("训练历史已保存到 models/training_history.npy")

    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        print("建议进一步减少批量大小或数据量")
    finally:
        # 清理内存
        del X_train, X_val
        gc.collect()
        print("内存清理完成")