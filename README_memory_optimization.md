# Noise2Void 内存优化训练指南

## 问题描述
原始训练脚本遇到内存错误：`Unable to allocate 16.1 GiB for an array`，这是因为同时加载了太多图像和生成了过多的训练patches。

## 解决方案

### 1. 系统资源检查
首先运行系统检查脚本了解你的硬件限制：
```bash
python check_system_resources.py
```

这个脚本会：
- 检查可用内存和GPU状态
- 分析FITS文件大小
- 提供针对你系统的训练参数建议

### 2. 训练脚本选择

#### 选项A: 使用批处理脚本（推荐）
```bash
run_training.bat
```
这个脚本会让你选择不同的训练模式。

#### 选项B: 直接运行Python脚本

**标准内存优化训练（推荐首选）：**
```bash
python memory_optimized_train.py
```
- 使用6个图像文件
- 每个图像生成80个patches
- 批量大小为4
- 包含内存监控

**极简内存训练（如果标准模式仍然内存不足）：**
```bash
python minimal_memory_train.py
```
- 只使用3个图像文件
- 总共生成200个patches
- 批量大小为2
- 最小内存占用

**修复后的原始脚本：**
```bash
python train_noise2void.py
```
- 使用8个图像文件
- 每个图像生成100个patches
- 批量大小为8

## 内存优化策略

### 1. 数据加载优化
- **批量加载**: 不再一次性加载所有38个FITS文件
- **限制文件数量**: 根据内存情况只加载6-8个文件
- **即时清理**: 处理完每个文件后立即释放内存

### 2. Patch生成优化
- **限制patch数量**: 每个图像只生成50-100个patches（原来可能生成数千个）
- **随机采样**: 从可能的patches中随机选择，保证训练数据的多样性
- **分步处理**: 逐个图像处理，避免同时在内存中保存大量数据

### 3. 训练参数优化
- **批量大小**: 从32减少到2-8，适应RTX 3050的4GB显存
- **训练步数**: 减少每轮训练步数，适应较少的数据量
- **学习率调整**: 适当提高学习率以补偿较小的批量大小

### 4. 内存管理
- **垃圾回收**: 在关键点主动调用`gc.collect()`
- **变量删除**: 及时删除不再需要的大型数组
- **内存监控**: 实时监控内存使用情况

## 预期效果

### 内存使用对比
- **原始方案**: 需要16.1 GB内存（失败）
- **标准优化**: 预计需要1-2 GB内存
- **极简模式**: 预计需要500 MB - 1 GB内存

### 训练数据量对比
- **原始方案**: 1,053,000个patches（过多）
- **标准优化**: 约480-800个patches
- **极简模式**: 约200个patches

## 故障排除

### 如果仍然出现内存错误：

1. **进一步减少数据量**：
   - 在脚本中将`max_files`改为2或3
   - 将`patches_per_image`改为30或50

2. **减少批量大小**：
   - 将`batch_size`改为1或2

3. **使用更小的patch尺寸**：
   - 将patch_shape改为(32, 32)

4. **关闭其他程序**：
   - 关闭浏览器和其他占用内存的程序

### 如果训练效果不佳：

1. **增加训练轮数**：
   - 将`train_epochs`从20-50增加到100

2. **调整学习率**：
   - 如果loss下降太慢，适当提高学习率
   - 如果训练不稳定，适当降低学习率

3. **增加数据量**（在内存允许的情况下）：
   - 逐步增加`max_files`和`patches_per_image`

## 监控训练过程

训练过程中会显示：
- 内存使用情况
- GPU内存使用情况
- 训练进度和loss值
- 预计完成时间

训练历史会保存在`models/`目录下，可以用于后续分析。

## 使用训练好的模型

### 1. 测试模型
```bash
C:\Python\Python310\python.exe test_trained_model.py
```
这会：
- 加载训练好的模型
- 对一个512x512的图像区域进行去噪测试
- 保存原始图像、去噪结果和对比图到`test_results/`目录
- 显示去噪效果统计信息

### 2. 批量处理整个数据集
```bash
C:\Python\Python310\python.exe batch_denoise.py
```
这会：
- 对`data/`目录中的所有FITS文件进行去噪处理
- 使用分块处理避免内存问题
- 保存去噪结果到`denoised_results/`目录
- 保持原始FITS文件的头信息

### 3. 训练结果
经过测试，模型表现良好：
- **PSNR**: 39.50 dB（较高的信噪比）
- **噪声标准差减少**: 328.44（显著的噪声降低）
- **处理速度**: 约0.25秒/512x512块
